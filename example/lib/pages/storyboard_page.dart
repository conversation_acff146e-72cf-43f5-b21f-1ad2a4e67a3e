import 'package:chat_flutter_example/widgets/arc_clipper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../ui/design_spec.dart';
import '../models/story_category.dart';
import '../models/story.dart';
import '../pages/story_detail_page.dart';
import '../widgets/story_category_chip.dart';

class StoryBoardPage extends StatefulWidget {
  const StoryBoardPage({super.key});

  @override
  State<StoryBoardPage> createState() => _StoryBoardPageState();
}

class _StoryBoardPageState extends State<StoryBoardPage> {
  final TextEditingController _searchCtrl = TextEditingController();
  bool _loading = false;
  String? _error;

  List<StoryCategory> _categories = [];
  String _selectedCategoryId = '';
  List<Story> _allStories = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initData();
    });
  }

  void _initData() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      // 从JSON文件加载故事数据
      final String response =
          await rootBundle.loadString('assets/data/stories.json');
      final data = await json.decode(response);

      // 加载分类
      final List<dynamic> categoriesData = data['categories'];
      _categories = categoriesData.map((cat) {
        return StoryCategory(
          id: cat['id'],
          name: _getLocalizedString(cat['nameKey']),
          icon: _getIconFromString(cat['icon']),
          color: _parseColor(cat['color']),
        );
      }).toList();

      if (_categories.isNotEmpty) {
        _selectedCategoryId = _categories.first.id;
      }

      // 加载故事
      final List<dynamic> storiesData = data['stories'];
      _allStories = storiesData.map((story) {
        return Story(
          id: story['id'],
          title: Map<String, String>.from(story['title']),
          categoryId: story['categoryId'],
          imageUrl: story['imageUrl'],
          popularity: story['popularity'],
          backgroundSetting:
              Map<String, String>.from(story['backgroundSetting']),
          characterSetting: Map<String, String>.from(story['characterSetting']),
          currentPlot: Map<String, String>.from(story['currentPlot']),
        );
      }).toList();
    } catch (e) {
      _error = AppLocalizations.of(context)!.loadFailed;
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  Color _parseColor(String hexColor) {
    return Color(int.parse(hexColor.substring(1), radix: 16) + 0xFF000000);
  }

  String _getLocalizedString(String key) {
    final localizations = AppLocalizations.of(context);
    switch (key) {
      case 'suspense':
        return localizations!.suspense;
      case 'sciFi':
        return localizations!.sciFi;
      case 'fantasy':
        return localizations!.fantasy;
      case 'history':
        return localizations!.history;
      case 'title_daily_1':
        return localizations!.storyEarlyDaysTitle;
      case 'desc_daily_1':
        return localizations!.storyEarlyDaysDesc;
      case 'title_adventure_1':
        return localizations!.storyHijrahTitle;
      case 'desc_adventure_1':
        return localizations!.storyHijrahDesc;
      case 'title_romance_1':
        return localizations!.storyPrincessTitle;
      case 'desc_romance_1':
        return localizations!.storyPrincessDesc;
      case 'title_fantasy_1':
        return localizations!.storyIbrahimTitle;
      case 'desc_fantasy_1':
        return localizations!.storyIbrahimDesc;
      case 'title_mystery_1':
        return localizations!.storyMusaTitle;
      case 'desc_mystery_1':
        return localizations!.storyMusaDesc;
      default:
        return key;
    }
  }

  IconData _getIconFromString(String iconName) {
    final iconMap = {
      'mystery': Icons.search,
      'science': Icons.science_outlined,
      'magic': Icons.auto_awesome,
      'history': Icons.history,
      'eco': Icons.eco,
      'park': Icons.park,
      'favorite': Icons.favorite,
      'person': Icons.person,
      'book': Icons.book,
      'star': Icons.star,
      'lightbulb': Icons.lightbulb,
      'school': Icons.school,
      'work': Icons.work,
      'home': Icons.home,
      'place': Icons.place,
      'public': Icons.public,
      'pets': Icons.pets,
      'nature': Icons.nature,
      'music': Icons.music_note,
      'art': Icons.brush,
      'sports': Icons.sports,
      'food': Icons.food_bank,
      'cake': Icons.cake,
      'icecream': Icons.icecream,
    };
    return iconMap[iconName] ?? Icons.book;
  }

  String _getLocalizedStringFromMap(Map<String, String> localizedMap) {
    final localizations = AppLocalizations.of(context);
    final String locale = localizations?.localeName ?? 'zh';
    return localizedMap[locale] ??
        localizedMap['zh'] ??
        localizedMap['en'] ??
        '';
  }

  List<Story> get _filteredStories {
    if (_allStories.isEmpty) return [];
    final q = _searchCtrl.text.trim().toLowerCase();
    final byCat = _allStories.where((s) =>
        _selectedCategoryId.isEmpty || s.categoryId == _selectedCategoryId);
    if (q.isEmpty) return byCat.toList();
    return byCat
        .where((s) =>
            _getLocalizedStringFromMap(s.title).toLowerCase().contains(q))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(_error!),
            const SizedBox(height: 8),
            ElevatedButton(
                onPressed: _initData,
                child: Text(AppLocalizations.of(context)!.retry)),
          ],
        ),
      );
    }

    return SafeArea(
      child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: RefreshIndicator(
            onRefresh: () async => _initData(),
            color: DesignSpec.primaryItemSelected,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics()),
              slivers: [
                SliverToBoxAdapter(child: _buildTopBar()),
                SliverToBoxAdapter(child: _buildSearchBar()),
                SliverToBoxAdapter(child: _buildCategories()),
                SliverToBoxAdapter(
                    child: _buildSectionTitle(
                        AppLocalizations.of(context)!.categoryStories)),
                _buildStoryHoriList(_filteredStories),
                SliverToBoxAdapter(
                    child: _buildSectionTitle(
                        AppLocalizations.of(context)!.mostPopular)),
                _buildPopularList(),
                const SliverToBoxAdapter(child: SizedBox(height: 100)),
              ],
            ),
          )),
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 16, 0, 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              AppLocalizations.of(context)!.appTitle,
              style: const TextStyle(
                fontSize: DesignSpec.fontSizeXl,
                fontWeight: DesignSpec.fontWeightBold,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.amber[200],
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                )
              ],
            ),
            child: Row(
              children: const [
                Icon(Icons.star, color: Color(0xFFEE864E), size: 18),
                SizedBox(width: 4),
                Text('00',
                    style: TextStyle(fontWeight: DesignSpec.fontWeightBold)),
              ],
            ),
          ),
          const SizedBox(width: 12),
          const CircleAvatar(
              radius: 18,
              backgroundColor: Colors.grey,
              child: Icon(Icons.person, color: Colors.white)),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(0),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            )
          ],
        ),
        child: Row(
          children: [
            const SizedBox(width: 16),
            const Icon(Icons.search, color: Colors.black54),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: _searchCtrl,
                onChanged: (_) => setState(() {}),
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context)!.searchHint,
                  border: InputBorder.none,
                ),
              ),
            ),
            IconButton(
              onPressed: () {},
              icon: const Icon(Icons.mic, color: Color(0xFFFF7F4D)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategories() {
    if (_categories.isEmpty) {
      return const SizedBox(height: 0);
    }
    return Padding(
      padding: const EdgeInsets.only(left: 0, right: 0, top: 8, bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.storyCategories,
            style: const TextStyle(
                fontSize: DesignSpec.fontSizeBase,
                fontWeight: DesignSpec.fontWeightBold),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              for (final c in _categories) ...[
                Expanded(child: StoryCategoryChip(
                  icon: c.icon,
                  label: c.name,
                  color: c.color,
                  selected: c.id == _selectedCategoryId,
                  onTap: () => setState(() => _selectedCategoryId = c.id),
                )),
                const SizedBox(width: 8),
              ]
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 4, 0, 8),
      child: Text(
        title,
        style: const TextStyle(
            fontSize: DesignSpec.fontSizeBase,
            fontWeight: DesignSpec.fontWeightBold),
      ),
    );
  }

  SliverToBoxAdapter _buildStoryHoriList(List<Story> stories) {
    return SliverToBoxAdapter(
      child: Container(
        height: 200,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: stories.length,
          itemBuilder: (context, index) {
            final s = stories[index];
            return Padding(
              padding: const EdgeInsets.only(right: 16),
              child: SizedBox(
                width: 140,
                child: _StoryCard(story: s, allCategories: _categories),
              ),
            );
          },
        ),
      ),
    );
  }

  SliverList _buildPopularList() {
    if (_allStories.isEmpty) {
      return SliverList(delegate: SliverChildListDelegate([]));
    }
    _allStories.sort((a, b) => b.popularity.compareTo(a.popularity));
    final popular = _allStories.take(3).toList();
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
            child: _PopularTile(story: popular[index], allCategories: _categories),
          );
        },
        childCount: popular.length,
      ),
    );
  }
}

class _StoryCard extends StatelessWidget {
  final Story story;
  final List<StoryCategory> _allCategories;

  const _StoryCard({required this.story, required List<StoryCategory> allCategories}) : _allCategories = allCategories;

  String _getLocalizedStringFromMap(
      BuildContext context, Map<String, String> localizedMap) {
    final localizations = AppLocalizations.of(context);
    final String locale = localizations?.localeName ?? 'zh';
    return localizedMap[locale] ??
        localizedMap['zh'] ??
        localizedMap['en'] ??
        '';
  }

  void _onStoryTap(BuildContext context) async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryDetailPage(
          story: story,
          categories: _allCategories,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => _onStoryTap(context),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ClipArcRRect(
              arcHeight: -6,
              borderRadius: 30,
              child: Image.asset(
                story.imageUrl,
                width: 140,
                height: 140,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.blue.shade300, Colors.purple.shade300],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.auto_stories,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  _getLocalizedStringFromMap(context, story.title),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                      fontSize: 13, fontWeight: DesignSpec.fontWeightSemiBold),
                )),
          ],
        ));
  }
}

class _PopularTile extends StatelessWidget {
  final Story story;
  final List<StoryCategory> _allCategories;

  const _PopularTile({required this.story, required List<StoryCategory> allCategories}) : _allCategories = allCategories;

  String _getLocalizedStringFromMap(
      BuildContext context, Map<String, String> localizedMap) {
    final localizations = AppLocalizations.of(context);
    final String locale = localizations?.localeName ?? 'zh';
    return localizedMap[locale] ??
        localizedMap['zh'] ??
        localizedMap['en'] ??
        '';
  }

  void _onPopularStoryTap(BuildContext context) async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryDetailPage(
          story: story,
          categories: _allCategories,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => _onPopularStoryTap(context),
        child: Stack(
          children: [
            Positioned.fill(
              top: 30,
              child: Container(
                height: 680,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 使用BulgeClipWidget实现枕头形状的图片裁剪效果
                ClipArcRRect(
                  arcHeight: 12,
                  borderRadius: 25,
                  child: Image.asset(
                    story.imageUrl,
                    width: double.infinity,
                    height: 180,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: double.infinity,
                        height: 120,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.grey.shade300, Colors.grey.shade400],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.white,
                            size: 40,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                  child: Row(
                    children: [
                      Text(
                        _getLocalizedStringFromMap(context, story.title),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.start,
                        style: const TextStyle(
                          fontWeight: DesignSpec.fontWeightSemiBold,
                          fontSize: DesignSpec.fontSizeBase,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.star,
                            color: Colors.amber.shade600,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${story.popularity}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                  child: Text(
                    _getLocalizedStringFromMap(context, story.backgroundSetting),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                      color: Colors.grey,
                      fontWeight: DesignSpec.fontWeightMedium,
                      fontSize: DesignSpec.fontSizeSm,
                    ),
                  ),
                ),
                SizedBox(height: 15),
              ],
            )
          ],
        ));
  }
}
